import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';
import { getCompanyHandle, getUserCompany } from '/@/api/common/api';

export const columns: BasicColumn[] = [
  {
    title: '资产编号',
    dataIndex: 'code',
    width: 120,
    fixed: 'left',
  },
  {
    title: '资产名称',
    dataIndex: 'name',
    width: 150,
    ellipsis: true,
  },
  {
    title: '空置资产名称',
    dataIndex: 'idleName',
    width: 150,
    ellipsis: true,
  },
  {
    title: '管理单位',
    dataIndex: 'manageUnitName',
    width: 200,
    ellipsis: true,
  },
  {
    title: '资产类型',
    dataIndex: 'type',
    width: 100,
    customRender: ({ text }) => {
      return render.renderDict(text, 'assets_type');
    },
  },
  {
    title: '是否报送国资委',
    dataIndex: 'reportOrNot',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(text, 'yes_no');
    },
  },
  {
    title: '空置起始日期',
    dataIndex: 'startDate',
    width: 120,
  },
  {
    title: '空置结束日期',
    dataIndex: 'endDate',
    width: 120,
  },
  {
    title: '空置天数',
    dataIndex: 'idleDays',
    width: 100,
    align: 'right',
  },
  {
    title: '空置面积(㎡)',
    dataIndex: 'idleArea',
    width: 120,
    align: 'right',
  },
  {
    title: '空置资产原值(万元)',
    dataIndex: 'assetsAmount',
    width: 150,
    align: 'right',
  },
  {
    title: '空置资产账面价值(万元)',
    dataIndex: 'bookAmount',
    width: 150,
    align: 'right',
  },
  {
    title: '账面价值时点',
    dataIndex: 'dateOfBookValue',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    customRender: ({ text }) => {
      return render.renderDict(text, 'record_status');
    },
  },
  {
    title: '空置原因',
    dataIndex: 'idleReason',
    width: 200,
    ellipsis: true,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 150,
    ellipsis: true,
  },
  {
    title: '经办人',
    dataIndex: 'operator',
    width: 100,
  },
  {
    title: '录入人',
    dataIndex: 'entryClerk',
    width: 100,
  },
  {
    title: '录入时间',
    dataIndex: 'createTime',
    width: 120,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 120,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '资产名称',
    field: 'name',
    component: 'Input',
    componentProps: {
      placeholder: '请输入资产名称',
    },
  },
  {
    label: '资产编号',
    field: 'code',
    component: 'Input',
    componentProps: {
      placeholder: '请输入资产编号',
    },
  },
  {
    label: '资产类型',
    field: 'type',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择资产类型',
      dictCode: 'assets_type',
    },
  },
  {
    label: '状态',
    field: 'status',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择状态',
      dictCode: 'record_status',
    },
  },
  {
    label: '空置资产名称',
    field: 'idleName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入空置资产名称',
    },
  },
  {
    label: '管理单位',
    field: 'manageUnits',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择管理单位',
      mode: 'multiple',
      api: getCompanyHandle,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
    },
  },
  {
    label: '空置起始日期',
    field: 'startDateRange',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '空置结束日期',
    field: 'endDateRange',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '空置天数',
    field: 'idleDays',
    component: 'JRangeNumber',
    componentProps: {
      placeholder: '最少天数',
      style: { width: '100%' },
    },
  },
  // {
  //   label: '空置天数最大值',
  //   field: 'maxIdleDays',
  //   component: 'InputNumber',
  //   componentProps: {
  //     placeholder: '最多天数',
  //     style: { width: '100%' },
  //   },
  // },
  {
    label: '空置面积',
    field: 'idleArea',
    component: 'JRangeNumber',
    componentProps: {
      placeholder: '最小面积(㎡)',
      precision: 2,
      style: { width: '100%' },
    },
  },
  // {
  //   label: '空置面积最大值',
  //   field: 'maxIdleArea',
  //   component: 'InputNumber',
  //   componentProps: {
  //     placeholder: '最大面积(㎡)',
  //     precision: 2,
  //     style: { width: '100%' },
  //   },
  // },
  {
    label: '账面价值',
    field: 'bookAmount',
    component: 'JRangeNumber',
    componentProps: {
      placeholder: '最小价值(万元)',
      precision: 2,
      style: { width: '100%' },
    },
  },
  // {
  //   label: '账面价值最大值',
  //   field: 'maxBookAmount',
  //   component: 'InputNumber',
  //   componentProps: {
  //     placeholder: '最大价值(万元)',
  //     precision: 2,
  //     style: { width: '100%' },
  //   },
  // },
  {
    label: '是否报送国资委',
    field: 'reportOrNot',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '全部',
      dictCode: 'yes_no',
    },
  },
  {
    label: '经办人',
    field: 'operator',
    component: 'Input',
    componentProps: {
      placeholder: '请输入经办人',
    },
  },
  {
    label: '录入人',
    field: 'entryClerk',
    component: 'Input',
    componentProps: {
      placeholder: '请输入录入人',
    },
  },
  {
    label: '录入时间',
    field: 'createTime',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '更新时间',
    field: 'updateTime',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
];

// 基本信息表单配置
export const basicInfoSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '序号',
    field: 'id',
    component: 'Input',
    componentProps: {
      placeholder: '序号为只读项',
      disabled: true,
    },
    helpMessage: '数据传到国资监管平台后将返回序号',
  },
  {
    label: '资产类型',
    field: 'type',
    component: 'Select',
    required: true,
    componentProps: {
      placeholder: '请选择资产类型',
      options: [
        { label: '土地', value: 0 },
        { label: '房屋', value: 1 },
        { label: '设备', value: 2 },
        { label: '广告位', value: 3 },
        { label: '其他', value: 4 },
      ],
    },
  },
  {
    label: '资产项目（资产名称）',
    field: 'name',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      placeholder: '请选择资产项目（资产名称）',
      api: '/api/asset/list',
      resultField: 'records',
      labelField: 'name',
      valueField: 'id',
      immediate: false,
      showSearch: true,
      filterOption: false,
      onSearch: (value) => {
        return { name: value };
      },
    },
  },
  {
    label: '资产编号',
    field: 'code',
    component: 'Input',
    componentProps: {
      placeholder: '选择资产项目（资产名称）后自动带出',
      disabled: true,
    },
  },
  {
    label: '闲置资产名称',
    field: 'idleName',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入闲置资产名称',
    },
  },
  {
    label: '管理单位',
    field: 'manageUnit',
    component: 'Select',
    required: true,
    componentProps: {
      placeholder: '选择资产项目（资产名称）后自动带出',
      disabled: true,
      options: [
        { label: '厦门市城市建设发展投资有限公司', value: '厦门市城市建设发展投资有限公司' },
        { label: '厦门市地热资源管理有限公司', value: '厦门市地热资源管理有限公司' },
        { label: '厦门兴地房屋征迁服务有限公司', value: '厦门兴地房屋征迁服务有限公司' },
        { label: '厦门地丰置业有限公司', value: '厦门地丰置业有限公司' },
        { label: '图智策划咨询（厦门）有限公司', value: '图智策划咨询（厦门）有限公司' },
        { label: '厦门市集众祥和物业管理有限公司', value: '厦门市集众祥和物业管理有限公司' },
        { label: '厦门市人居乐业物业服务有限公司', value: '厦门市人居乐业物业服务有限公司' },
      ],
    },
    helpMessage: '将使用管理单位作为数据权限判断依据',
  },
  {
    label: '是否报送国资委',
    field: 'reportOrNot',
    component: 'Select',
    required: true,
    componentProps: {
      placeholder: '请选择',
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 },
      ],
    },
  },
  {
    label: '经办人',
    field: 'operator',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入经办人',
    },
  },
  {
    label: '录入人',
    field: 'entryClerk',
    component: 'Input',
    componentProps: {
      placeholder: '录入人',
      disabled: true,
    },
  },
  {
    label: '录入时间',
    field: 'createTime',
    component: 'DatePicker',
    componentProps: {
      placeholder: '录入时间',
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      disabled: true,
    },
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    required: true,
    componentProps: {
      placeholder: '请选择状态',
      options: [
        { label: '草稿', value: 0 },
        { label: '备案', value: 1 },
        { label: '撤回', value: 2 },
        { label: '作废', value: 4 },
      ],
    },
    helpMessage: '备案数据支持撤回、草稿数据和撤回数据支持作废',
  },
];

// 空置信息表单配置
export const vacantInfoSchema: FormSchema[] = [
  {
    label: '空置起始日期',
    field: 'startDate',
    component: 'DatePicker',
    required: true,
    componentProps: {
      placeholder: '请选择空置起始日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      disabledDate: (current) => {
        return current && current > new Date();
      },
    },
  },
  {
    label: '空置结束日期',
    field: 'endDate',
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择空置结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      disabledDate: (current, formModel) => {
        if (!formModel.startDate) return false;
        return current && current <= new Date(formModel.startDate);
      },
    },
  },
  {
    label: '空置天数',
    field: 'idleDays',
    component: 'Input',
    componentProps: {
      placeholder: '空置天数',
      disabled: true,
    },
    helpMessage: '空置天数180天(含)以内的数据属于【空置信息】，空置天数180天以上的数据属于【闲置信息】',
  },
  {
    label: '空置面积（㎡）',
    field: 'idleArea',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入空置面积',
      precision: 2,
      min: 0,
      style: { width: '100%' },
    },
    dynamicRules: ({ model }) => {
      return [
        {
          required: model.type === 0 || model.type === 1,
          message: '请输入空置面积（㎡）',
          trigger: 'blur',
        },
      ];
    },
  },
  {
    label: '空置资产原值（万元）',
    field: 'assetsAmount',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入空置资产原值',
      precision: 2,
      min: 0,
      style: { width: '100%' },
    },
  },
  {
    label: '空置资产账面价值（万元）',
    field: 'bookAmount',
    component: 'InputNumber',
    required: true,
    componentProps: {
      placeholder: '请输入空置资产账面价值',
      precision: 2,
      min: 0,
      style: { width: '100%' },
    },
  },
  {
    label: '账面价值时点',
    field: 'dateOfBookValue',
    component: 'DatePicker',
    required: true,
    componentProps: {
      placeholder: '请选择账面价值时点',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '空置原因',
    field: 'idleReason',
    component: 'InputTextArea',
    required: true,
    componentProps: {
      placeholder: '请输入空置原因',
      rows: 4,
    },
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入备注',
      rows: 4,
    },
  },
];

// 盘活记录表格列配置
export const dealRecordColumns = [
  {
    title: '日期',
    dataIndex: 'date',
    width: 180,
    key: 'date',
  },
  {
    title: '是否已盘活',
    dataIndex: 'isResult',
    width: 150,
    key: 'isResult',
  },
  {
    title: '盘活方式',
    dataIndex: 'vitalizeType',
    width: 180,
    key: 'vitalizeType',
  },
  {
    title: '已采取的盘活管理措施',
    dataIndex: 'reason',
    width: 300,
    key: 'reason',
  },
  {
    title: '下一步建议',
    dataIndex: 'nextReason',
    width: 300,
    key: 'nextReason',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 80,
    key: 'action',
    fixed: 'right',
  },
];

// 完整的表单配置（兼容旧版本）
export const formSchema: FormSchema[] = [
  ...basicInfoSchema,
  ...vacantInfoSchema,
]; 