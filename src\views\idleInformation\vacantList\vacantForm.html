<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <meta name="prototype-page-id" content="vacantForm"> <!-- 添加此行指定当前页面ID -->
    <script src="annotationsData.js" defer></script>
    <script src="prototypeAnnotations.js" defer></script>
    
    <title>空置信息表单</title>
    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://demo.axureux.com/fontawesome/5.7.2/pro/css/all.min.css">
    <!-- 引入ElementUI组件库 -->
    <link rel="stylesheet" href="assets/element-ui/index.css">
    <style>
        :root {
            --primary-color: #1890ff;
            --success-color: #52c41a;
            --warning-color: #faad14;
            --error-color: #f5222d;
            --font-size-base: 14px;
            --heading-color: rgba(0, 0, 0, 0.85);
            --text-color: rgba(0, 0, 0, 0.65);
            --disabled-color: rgba(0, 0, 0, 0.25);
            --border-color-base: #d9d9d9;
            --box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            font-size: var(--font-size-base);
            color: var(--text-color);
            background-color: #f5f7fa;
            line-height: 1.5;
        }

        .container {
            width: 100%;
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 15px;
        }

        .page-header {
            margin-bottom: 20px;
            padding: 16px 0;
            border-bottom: 1px solid var(--border-color-base);
        }

        .page-title {
            font-size: 24px;
            color: var(--heading-color);
            font-weight: 500;
        }

        .form-card {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: var(--box-shadow-base);
            margin-bottom: 24px;
        }

        .form-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-base);
            background-color: #fafafa;
            border-radius: 4px 4px 0 0;
        }

        .form-card-title {
            font-size: 16px;
            color: var(--heading-color);
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .form-card-title i {
            margin-right: 8px;
            color: var(--primary-color);
        }

        .form-card-body {
            padding: 24px;
        }

        .form-footer {
            text-align: center;
            padding: 24px 0;
        }

        .required:before {
            content: "*";
            color: var(--error-color);
            margin-right: 4px;
        }

        /* 自定义ElementUI样式 */
        .el-form-item {
            margin-bottom: 22px;
        }

        .el-textarea__inner {
            min-height: 120px !important;
        }

        .el-form-item__label {
            font-weight: 500;
        }

        .el-button--primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .el-button--primary:hover,
        .el-button--primary:focus {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }

        .el-date-editor.el-input {
            width: 100%;
        }

        .el-select {
            width: 100%;
        }

        .el-input-number {
            width: 100%;
        }

        /* 提示图标样式 */
        .tooltip-icon {
            color: #909399;
            margin-left: 5px;
            cursor: pointer;
        }

        /* 去除Vue初始化闪烁 */
        [v-cloak] {
            display: none;
        }

        /* 盘活记录表格样式 */
        .table-container {
            margin-top: 16px;
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        /* 表格中输入框对齐样式 */
        .record-table-form-item .el-form-item__content {
            line-height: normal;
            width: 100%;
        }

        .record-table-form-item.el-form-item {
            margin-bottom: 0;
        }

        .el-table .el-form-item {
            display: flex;
            margin-bottom: 0;
        }

        .el-table .el-form-item__content {
            flex: 1;
            margin-left: 0 !important;
        }

        .el-table .cell {
            padding: 8px 10px;
        }

        .empty-text {
            text-align: center;
            color: #909399;
            padding: 30px 0;
        }

        /* 帮助文本样式 */
        .help-text {
            font-size: 12px;
            color: #909399;
            line-height: 1.5;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div id="app" v-cloak>
        <div class="container">
            <div class="page-header">
                <h1 class="page-title">空置信息表单</h1>
            </div>

            <el-form :model="formData" :rules="rules" ref="vacantForm" label-width="180px" size="small">
                <!-- 基本信息 -->
                <div class="form-card">
                    <div class="form-card-header">
                        <h2 class="form-card-title">
                            <i class="fas fa-info-circle"></i> 基本信息
                        </h2>
                    </div>
                    <div class="form-card-body">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="id">
                                    <template slot="label">
                                        <span>序号</span>
                                        <i class="fas fa-question-circle tooltip-icon" @click="showTip('数据传到国资监管平台后将返回序号')"></i>
                                    </template>
                                    <el-input v-model="formData.id" placeholder="序号为只读项" disabled></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="type">
                                    <template slot="label">
                                        <span>资产类型</span>
                                    </template>
                                    <el-select v-model="formData.type" placeholder="请选择资产类型" @change="handleAssetTypeChange">
                                        <el-option :key="0" label="土地" :value="0"></el-option>
                                        <el-option :key="1" label="房屋" :value="1"></el-option>
                                        <el-option :key="2" label="设备" :value="2"></el-option>
                                        <el-option :key="3" label="广告位" :value="3"></el-option>
                                        <el-option :key="4" label="其他" :value="4"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="name">
                                    <template slot="label">
                                        <span>资产项目（资产名称）</span>
                                    </template>
                                    <el-select
                                        v-model="formData.name"
                                        filterable
                                        remote
                                        reserve-keyword
                                        placeholder="请选择资产项目（资产名称）"
                                        :remote-method="remoteAssetSearch"
                                        :loading="loading"
                                        @change="handleAssetNameChange">
                                        <el-option
                                            v-for="item in assetOptions"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="code">
                                    <template slot="label">
                                        <span>资产编号</span>
                                    </template>
                                    <el-input v-model="formData.code" placeholder="选择资产项目（资产名称）后自动带出" disabled></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="idleName">
                                    <template slot="label">
                                        <span>闲置资产名称</span>
                                    </template>
                                    <el-input v-model="formData.idleName" placeholder="请输入闲置资产名称"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="manageUnit">
                                    <template slot="label">
                                        <span class="required">管理单位</span>
                                    </template>
                                    <el-input v-model="formData.manageUnit" placeholder="选择资产项目（资产名称）后自动带出" disabled></el-input>
                                    <div class="help-text">将使用管理单位作为数据权限判断依据</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="是否报送国资委" prop="reportOrNot">
                                    <el-select v-model="formData.reportOrNot" placeholder="请选择">
                                        <el-option label="否" :value="0"></el-option>
                                        <el-option label="是" :value="1"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="经办人" prop="operator">
                                    <el-input v-model="formData.operator" placeholder="请输入经办人"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="录入人" prop="entryClerk">
                                    <el-input v-model="formData.entryClerk" disabled></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="录入时间" prop="createTime">
                                    <el-input v-model="formData.createTime" disabled></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="status">
                                    <template slot="label">
                                        <span>状态</span>
                                        <i class="fas fa-question-circle tooltip-icon" @click="showTip('备案数据支持撤回、草稿数据和撤回数据支持作废')"></i>
                                    </template>
                                    <el-select v-model="formData.status" placeholder="请选择状态">
                                        <el-option :key="0" label="草稿" :value="0"></el-option>
                                        <el-option :key="1" label="备案" :value="1"></el-option>
                                        <el-option :key="2" label="撤回" :value="2" disabled></el-option>
                                        <el-option :key="4" label="作废" :value="4" disabled></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </div>

                <!-- 空置信息 -->
                <div class="form-card">
                    <div class="form-card-header">
                        <h2 class="form-card-title">
                            <i class="fas fa-building"></i> 空置信息
                        </h2>
                    </div>
                    <div class="form-card-body">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="startDate">
                                    <template slot="label">
                                        <span>空置起始日期</span>
                                    </template>
                                    <el-date-picker
                                        v-model="formData.startDate"
                                        type="date"
                                        placeholder="请选择空置起始日期"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        :picker-options="startDateOptions"
                                        @change="calculateIdleDays"
                                        style="width: 100%;">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="endDate">
                                    <template slot="label">
                                        <span>空置结束日期</span>
                                    </template>
                                    <el-date-picker
                                        v-model="formData.endDate"
                                        type="date"
                                        placeholder="请选择空置结束日期"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        :picker-options="endDateOptions"
                                        @change="calculateIdleDays"
                                        style="width: 100%;">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="空置天数">
                                    <el-input v-model="formData.idleDays" disabled></el-input>
                                    <div class="help-text">空置天数180天(含)以内的数据属于【空置信息】，空置天数180天以上的数据属于【闲置信息】</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="idleArea" :rules="[{ required: formData.type === 0 || formData.type === 1, message: '请输入空置面积（㎡）', trigger: 'blur' }]">
                                    <template slot="label">
                                        <span>空置面积（㎡）</span>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.idleArea" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入空置面积"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="assetsAmount">
                                    <template slot="label">
                                        <span>空置资产原值（万元）</span>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.assetsAmount" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入空置资产原值"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="bookAmount">
                                    <template slot="label">
                                        <span>空置资产账面价值（万元）</span>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.bookAmount" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入空置资产账面价值"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="dateOfBookValue">
                                    <template slot="label">
                                        <span>账面价值时点</span>
                                    </template>
                                    <el-date-picker
                                        v-model="formData.dateOfBookValue"
                                        type="date"
                                        placeholder="请选择账面价值时点"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        style="width: 100%;">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item prop="idleReason">
                                    <template slot="label">
                                        <span>空置原因</span>
                                    </template>
                                    <el-input 
                                        type="textarea" 
                                        v-model="formData.idleReason" 
                                        placeholder="请输入空置原因" 
                                        :rows="4">
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item prop="remark">
                                    <template slot="label">
                                        <span>备注</span>
                                    </template>
                                    <el-input 
                                        type="textarea" 
                                        v-model="formData.remark" 
                                        placeholder="请输入备注" 
                                        :rows="4">
                                    </el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </div>

                <!-- 盘活记录 -->
                <div class="form-card">
                    <div class="form-card-header">
                        <h2 class="form-card-title">
                            <i class="fas fa-sync-alt"></i> 盘活记录
                        </h2>
                        <div class="table-header">
                            <el-button type="primary" size="small" icon="el-icon-plus" @click="addRecord">新增盘活记录</el-button>
                        </div>
                    </div>
                    <div class="form-card-body">
                        <el-table
                            :data="formData.dealList"
                            border
                            style="width: 100%">
                            <el-table-column prop="date" label="日期" min-width="180">
                                <template slot="header">
                                    <span class="required">日期</span>
                                </template>
                                <template slot-scope="scope">
                                    <el-form-item
                                        :prop="'dealList.' + scope.$index + '.date'"
                                        :rules="{ required: true, message: '请选择日期', trigger: 'change' }"
                                        class="record-table-form-item">
                                        <el-date-picker
                                            v-model="scope.row.date"
                                            type="date"
                                            placeholder="请选择日期"
                                            format="yyyy-MM-dd"
                                            value-format="yyyy-MM-dd"
                                            style="width: 100%;">
                                        </el-date-picker>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column prop="isResult" min-width="150">
                                <template slot="header">
                                    <span class="required">是否已盘活</span>
                                </template>
                                <template slot-scope="scope">
                                    <el-form-item
                                        :prop="'dealList.' + scope.$index + '.isResult'"
                                        :rules="{ required: true, message: '请选择是否已盘活', trigger: 'change' }"
                                        class="record-table-form-item">
                                        <el-select v-model="scope.row.isResult" placeholder="请选择">
                                            <el-option label="否" :value="0"></el-option>
                                            <el-option label="是" :value="1"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column prop="vitalizeType" min-width="180">
                                <template slot="header">
                                    <span class="required">盘活方式</span>
                                </template>
                                <template slot-scope="scope">
                                    <el-form-item
                                        :prop="'dealList.' + scope.$index + '.vitalizeType'"
                                        :rules="{ required: true, message: '请选择盘活方式', trigger: 'change' }"
                                        class="record-table-form-item">
                                        <el-select v-model="scope.row.vitalizeType" placeholder="请选择">
                                            <el-option label="出租" :value="0"></el-option>
                                            <el-option label="出售" :value="1"></el-option>
                                            <el-option label="资产证券化" :value="2"></el-option>
                                            <el-option label="收储" :value="3"></el-option>
                                            <el-option label="转为自用" :value="4"></el-option>
                                            <el-option label="转为借用" :value="5"></el-option>
                                            <el-option label="转为占用" :value="6"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column prop="reason" label="已采取的盘活管理措施" min-width="300">
                                <template slot-scope="scope">
                                    <el-form-item 
                                        :prop="'dealList.' + scope.$index + '.reason'"
                                        class="record-table-form-item">
                                        <el-input 
                                            type="textarea" 
                                            v-model="scope.row.reason" 
                                            placeholder="请输入已采取的盘活管理措施" 
                                            :rows="2">
                                        </el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column prop="nextReason" label="下一步建议" min-width="300">
                                <template slot-scope="scope">
                                    <el-form-item 
                                        :prop="'dealList.' + scope.$index + '.nextReason'"
                                        class="record-table-form-item">
                                        <el-input 
                                            type="textarea" 
                                            v-model="scope.row.nextReason" 
                                            placeholder="请输入下一步建议" 
                                            :rows="2">
                                        </el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="80" fixed="right">
                                <template slot-scope="scope">
                                    <el-button
                                        type="danger"
                                        size="mini"
                                        icon="el-icon-delete"
                                        circle
                                        @click="removeRecord(scope.$index)">
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div v-if="formData.dealList.length === 0" class="empty-text">
                            暂无盘活记录，请点击上方"新增盘活记录"按钮添加。
                        </div>
                    </div>
                </div>
                
                <!-- 表单提交按钮 -->
                <div class="form-footer">
                    <el-button @click="resetForm">重置</el-button>
                    <el-button type="primary" @click="submitForm">提交</el-button>
                </div>
            </el-form>
        </div>
    </div>

    <!-- 引入Vue.js -->
    <script src="https://cdn.bootcdn.net/ajax/libs/vue/2.6.14/vue.min.js"></script>
    <!-- 引入ElementUI组件库 -->
    <script src="assets/element-ui/index.js"></script>
    <!-- 引入axios -->
    <script src="https://cdn.bootcdn.net/ajax/libs/axios/0.21.1/axios.min.js"></script>

    <script>
        new Vue({
            el: '#app',
            data() {
                // 定义资产类型验证函数
                const validateIdleArea = (rule, value, callback) => {
                    if ((this.formData.type === 0 || this.formData.type === 1) && (value === '' || value === null || value === undefined)) {
                        callback(new Error('请输入空置面积'));
                    } else {
                        callback();
                    }
                };
                
                // 定义日期验证函数
                const validateEndDate = (rule, value, callback) => {
                    if (!value) {
                        callback();
                        return;
                    }
                    
                    if (!this.formData.startDate) {
                        callback(new Error('请先选择空置起始日期'));
                        return;
                    }
                    
                    const startDate = new Date(this.formData.startDate);
                    const endDate = new Date(value);
                    
                    if (endDate <= startDate) {
                        callback(new Error('空置结束日期必须大于空置起始日期'));
                    } else {
                        callback();
                    }
                };
                
                return {
                    loading: false,
                    // 表单数据
                    formData: {
                        // 基本信息
                        id: '', // 序号，系统生成
                        type: '', // 资产类型
                        name: '', // 资产名称
                        code: '', // 资产编号
                        idleName: '', // 闲置资产名称
                        manageUnit: '', // 管理单位
                        reportOrNot: '', // 是否报送国资委
                        operator: '当前用户', // 经办人
                        entryClerk: '当前用户', // 录入人
                        createTime: '', // 录入时间
                        status: 0, // 状态，默认草稿
                        
                        // 空置信息
                        startDate: '', // 空置起始日期
                        endDate: '', // 空置结束日期
                        idleDays: '', // 空置天数（计算得出）
                        idleArea: '', // 空置面积
                        assetsAmount: '', // 空置资产原值
                        bookAmount: '', // 空置资产账面价值
                        dateOfBookValue: '', // 账面价值时点
                        idleReason: '', // 空置原因
                        remark: '', // 备注
                        
                        // 盘活记录
                        dealList: [], // 盘活记录列表
                    },
                    
                    // 表单验证规则
                    rules: {
                        // 基本信息验证规则
                        type: [
                            { required: true, message: '请选择资产类型', trigger: 'change' }
                        ],
                        name: [
                            { required: true, message: '请选择资产项目（资产名称）', trigger: 'change' }
                        ],
                        code: [
                            { required: true, message: '请选择资产项目（资产名称）后自动带出资产编号', trigger: 'change' }
                        ],
                        manageUnit: [
                            { required: true, message: '管理单位为必填项', trigger: 'change' }
                        ],
                        reportOrNot: [
                            { required: true, message: '请选择是否报送国资委', trigger: 'change' }
                        ],
                        operator: [
                            { required: true, message: '请输入经办人', trigger: 'blur' }
                        ],
                        entryClerk: [
                            { required: true, message: '录入人为必填项' }
                        ],
                        createTime: [
                            { required: true, message: '录入时间为必填项' }
                        ],
                        status: [
                            { required: true, message: '请选择状态', trigger: 'change' }
                        ],
                        
                        // 空置信息验证规则
                        startDate: [
                            { required: true, message: '请选择空置起始日期', trigger: 'change' }
                        ],
                        endDate: [
                            { validator: validateEndDate, trigger: 'change' }
                        ],
                        idleArea: [
                            { validator: validateIdleArea, trigger: 'blur' }
                        ],
                        bookAmount: [
                            { required: true, message: '请输入空置资产账面价值', trigger: 'blur' }
                        ],
                        dateOfBookValue: [
                            { required: true, message: '请选择账面价值时点', trigger: 'change' }
                        ],
                        idleReason: [
                            { required: true, message: '请输入空置原因', trigger: 'blur' }
                        ],
                    },
                    
                    // 资产选项 (模拟数据)
                    assetOptions: [],
                    
                    // 企业选项 (模拟数据)
                    enterpriseOptions: [],

                    // 日期选择器配置
                    startDateOptions: {
                        disabledDate(time) {
                            return time.getTime() > Date.now();
                        }
                    },
                    endDateOptions: {
                        disabledDate: (time) => {
                            if (!this.formData.startDate) {
                                return false;
                            }
                            
                            const startDate = new Date(this.formData.startDate).getTime();
                            return startDate >= time.getTime();
                        }
                    }
                };
            },
            
            created() {
                // 初始化模拟企业数据
                this.initEnterpriseOptions();
                // 初始化模拟资产数据
                this.initAssetOptions();
                // 初始化录入时间
                this.formData.createTime = this.formatDateTime(new Date());
            },
            
            methods: {
                // 格式化日期时间
                formatDateTime(date) {
                    const y = date.getFullYear();
                    const m = (date.getMonth() + 1).toString().padStart(2, '0');
                    const d = date.getDate().toString().padStart(2, '0');
                    const h = date.getHours().toString().padStart(2, '0');
                    const i = date.getMinutes().toString().padStart(2, '0');
                    const s = date.getSeconds().toString().padStart(2, '0');
                    return `${y}-${m}-${d} ${h}:${i}:${s}`;
                },
                // 初始化企业选项
                initEnterpriseOptions() {
                    // 模拟企业数据
                    this.enterpriseOptions = [
                        { value: 0, label: '厦门市城市建设发展投资有限公司' },
                        { value: 1, label: '厦门市地热资源管理有限公司' },
                        { value: 2, label: '厦门兴地房屋征迁服务有限公司' },
                        { value: 3, label: '厦门地丰置业有限公司' },
                        { value: 4, label: '图智策划咨询（厦门）有限公司' },
                        { value: 5, label: '厦门市集众祥和物业管理有限公司' },
                        { value: 6, label: '厦门市人居乐业物业服务有限公司' },
                    ];
                },
                // 初始化资产选项
                initAssetOptions() {
                    // 模拟10条资产数据供选择
                    const enterpriseLabels = this.enterpriseOptions.map(e => e.label);
                    this.assetOptions = [
                        { value: '001', label: '办公楼A栋(001)', manageUnit: enterpriseLabels[0] },
                        { value: '002', label: '办公楼B栋(002)', manageUnit: enterpriseLabels[1] },
                        { value: '003', label: '生产车间1(003)', manageUnit: enterpriseLabels[2] },
                        { value: '004', label: '生产车间2(004)', manageUnit: enterpriseLabels[3] },
                        { value: '005', label: '设备车间(005)', manageUnit: enterpriseLabels[4] },
                        { value: '006', label: '物料仓库(006)', manageUnit: enterpriseLabels[5] },
                        { value: '007', label: '成品仓库(007)', manageUnit: enterpriseLabels[6] },
                        { value: '008', label: '综合办公室(008)', manageUnit: enterpriseLabels[0] },
                        { value: '009', label: '会议中心(009)', manageUnit: enterpriseLabels[1] },
                        { value: '010', label: '检测中心(010)', manageUnit: enterpriseLabels[2] },
                    ];
                },
                
                // 远程搜索资产
                remoteAssetSearch(query) {
                    if (query === '') {
                        this.assetOptions = [];
                        return;
                    }
                    
                    this.loading = true;
                    
                    // 模拟远程搜索延迟
                    setTimeout(() => {
                        this.loading = false;
                        // 过滤包含查询字符的资产选项
                        this.assetOptions = this.assetOptions.filter(item => {
                            return item.label.toLowerCase().includes(query.toLowerCase());
                        });
                    }, 200);
                },
                
                // 处理资产类型变更
                handleAssetTypeChange(value) {
                    // 更新空置面积必填状态
                    this.$refs.vacantForm.validateField('idleArea');
                },
                
                // 处理资产名称变更
                handleAssetNameChange(value) {
                    // 根据选择的资产名称，自动带出资产编号
                    const selectedAsset = this.assetOptions.find(item => item.value === value);
                    if (selectedAsset) {
                        // 提取资产名称中括号内的编号，并设置到资产编号字段
                        const codeMatch = selectedAsset.label.match(/\((\d+)\)/);
                        if (codeMatch && codeMatch[1]) {
                            this.formData.code = codeMatch[1];
                        }
                        // 自动带出管理单位
                        this.formData.manageUnit = selectedAsset.manageUnit || '';
                    }
                },
                
                // 计算空置天数
                calculateIdleDays() {
                    if (!this.formData.startDate) {
                        this.formData.idleDays = '';
                        return;
                    }
                    
                    const startDate = new Date(this.formData.startDate);
                    let endDate;
                    
                    if (this.formData.endDate) {
                        endDate = new Date(this.formData.endDate);
                    } else {
                        endDate = new Date(); // 使用当前日期作为结束日期
                    }
                    
                    // 计算天数差异
                    const diffTime = Math.abs(endDate - startDate);
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                    
                    this.formData.idleDays = diffDays;
                },
                
                // 添加盘活记录
                addRecord() {
                    this.formData.dealList.push({
                        date: '',
                        isResult: '',
                        vitalizeType: '',
                        reason: '',
                        nextReason: ''
                    });
                },
                
                // 移除盘活记录
                removeRecord(index) {
                    this.$confirm('确定要删除该盘活记录吗?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.formData.dealList.splice(index, 1);
                        this.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                    }).catch(() => {
                        this.$message({
                            type: 'info',
                            message: '已取消删除'
                        });          
                    });
                },
                
                // 显示提示信息
                showTip(message) {
                    this.$message({
                        message: message,
                        type: 'info'
                    });
                },
                
                // 提交表单
                submitForm() {
                    this.$refs.vacantForm.validate((valid) => {
                        if (valid) {
                            // 显示加载状态
                            const loading = this.$loading({
                                lock: true,
                                text: '提交中...',
                                spinner: 'el-icon-loading',
                                background: 'rgba(0, 0, 0, 0.7)'
                            });
                            
                            // 模拟API提交
                            setTimeout(() => {
                                loading.close();
                                
                                // 生成序号（实际应该由后端生成）
                                this.formData.id = 'KZXX' + new Date().getTime().toString().substring(6);
                                
                                this.$message({
                                    type: 'success',
                                    message: '表单提交成功！序号：' + this.formData.id
                                });
                                
                                // 实际项目中应该在这里调用API提交数据
                                // axios.post('/api/vacant/submit', this.formData)
                                //     .then(response => {
                                //         if (response.data.code === 0) {
                                //             this.$message({
                                //                 type: 'success',
                                //                 message: '表单提交成功！'
                                //             });
                                //             // 更新序号
                                //             this.formData.id = response.data.data.id;
                                //         } else {
                                //             this.$message.error(response.data.message || '提交失败');
                                //         }
                                //     })
                                //     .catch(error => {
                                //         console.error(error);
                                //         this.$message.error('网络错误，请稍后重试');
                                //     });
                            }, 1500);
                        } else {
                            this.$message.error('表单验证失败，请检查必填项');
                            return false;
                        }
                    });
                },
                
                // 重置表单
                resetForm() {
                    this.$confirm('确定要重置表单吗？所有已填写的数据将会清空', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.$refs.vacantForm.resetFields();
                        this.formData.dealList = [];
                        // 重置时更新录入时间
                        this.formData.createTime = this.formatDateTime(new Date());
                        this.formData.operator = '当前用户';
                        this.formData.entryClerk = '当前用户';
                        this.$message({
                            type: 'success',
                            message: '表单已重置'
                        });
                    }).catch(() => {
                        this.$message({
                            type: 'info',
                            message: '已取消重置操作'
                        });          
                    });
                }
            }
        });
    </script>
</body>
</html>
