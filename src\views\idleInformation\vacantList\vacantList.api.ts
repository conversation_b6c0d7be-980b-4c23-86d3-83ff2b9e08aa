import { defHttp } from '/@/utils/http/axios';
import { Modal } from 'ant-design-vue';

enum Api {
  list = '/biz/assets/idle/queryPage',
  save = '/mock/vacant/add',
  edit = '/mock/vacant/edit',
  delete = '/mock/vacant/delete',
  deleteBatch = '/mock/vacant/deleteBatch',
  importExcel = '/mock/vacant/importExcel',
  exportXls = '/mock/vacant/exportXls',
  exportAll = '/mock/vacant/exportAll',
  assetList = '/mock/asset/list',
  downloadTemplate = '/mock/vacant/downloadTemplate',
  detail = '/mock/vacant/detail',
}

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.post({ url: Api.list, params });

/**
 * 保存或更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};

/**
 * 获取详情
 * @param id
 */
export const getDetail = (id) => defHttp.get({ url: Api.detail, params: { id } });

/**
 * 删除
 * @param params
 * @param handleSuccess
 */
export const deleteVacant = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.delete, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDeleteVacant = (params, handleSuccess) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 导入
 * @param params
 */
export const importExcel = (params) => defHttp.post({ url: Api.importExcel, params });

/**
 * 导出
 * @param params
 */
export const exportVacant = (params) => defHttp.get({ url: Api.exportXls, params, responseType: 'blob' });

/**
 * 全部导出
 * @param params
 */
export const exportAllVacant = (params) => defHttp.get({ url: Api.exportAll, params, responseType: 'blob' });

/**
 * 获取资产列表
 * @param params
 */
export const getAssetList = (params) => defHttp.get({ url: Api.assetList, params });

/**
 * 下载导入模板
 */
export const downloadTemplate = () => defHttp.get({ url: Api.downloadTemplate, responseType: 'blob' }); 